' ========================================
' VBA修正代码：变量作用域问题的三种解决方案
' ========================================

' ========================================
' 解决方案1：使用全局变量（推荐用于简单场景）
' ========================================

Public currentRow As Long  ' 声明为全局变量

Sub 列出目录结构_方案1()
    Dim folderPath As String
    
    '让用户选择要列出结构的文件夹
    With Application.FileDialog(msoFileDialogFolderPicker)
        .Title = "请选择要列出结构的文件夹"
        If .Show = -1 Then
            folderPath = .SelectedItems(1)
        Else
            Exit Sub
        End If
    End With
    
    '清空当前工作表
    ActiveSheet.Cells.Clear
    
    '写入表头
    Cells(1, 1) = "文件路径"
    Cells(1, 2) = "类型"
    Cells(1, 3) = "大小(字节)"
    
    '初始化全局变量
    currentRow = 2
    
    '调用递归函数列出目录结构
    ListFiles_方案1 folderPath, ""
End Sub

Sub ListFiles_方案1(ByVal folderPath As String, ByVal indent As String)
    Dim fso As Object
    Dim folder As Object
    Dim subfolder As Object
    Dim file As Object
    
    Set fso = CreateObject("Scripting.FileSystemObject")
    Set folder = fso.GetFolder(folderPath)
    
    For Each file In folder.Files
        Cells(currentRow, 1) = indent & file.Path  ' 现在可以正常访问全局变量
        Cells(currentRow, 2) = "文件"
        Cells(currentRow, 3) = file.Size
        currentRow = currentRow + 1
    Next file
    
    For Each subfolder In folder.SubFolders
        Cells(currentRow, 1) = indent & subfolder.Path
        Cells(currentRow, 2) = "文件夹"
        currentRow = currentRow + 1
        ListFiles_方案1 subfolder.Path, indent & "    "
    Next subfolder
End Sub

' ========================================
' 解决方案2：通过参数传递（推荐用于复杂场景）
' ========================================

Sub 列出目录结构_方案2()
    Dim folderPath As String
    Dim currentRow As Long
    
    With Application.FileDialog(msoFileDialogFolderPicker)
        .Title = "请选择要列出结构的文件夹"
        If .Show = -1 Then
            folderPath = .SelectedItems(1)
        Else
            Exit Sub
        End If
    End With
    
    ActiveSheet.Cells.Clear
    
    Cells(1, 1) = "文件路径"
    Cells(1, 2) = "类型"
    Cells(1, 3) = "大小(字节)"
    
    currentRow = 2
    
    ' 将currentRow作为引用参数传递
    ListFiles_方案2 folderPath, "", currentRow
End Sub

Sub ListFiles_方案2(ByVal folderPath As String, ByVal indent As String, ByRef currentRow As Long)
    Dim fso As Object
    Dim folder As Object
    Dim subfolder As Object
    Dim file As Object
    
    Set fso = CreateObject("Scripting.FileSystemObject")
    Set folder = fso.GetFolder(folderPath)
    
    For Each file In folder.Files
        Cells(currentRow, 1) = indent & file.Path  ' 通过参数传递的值
        Cells(currentRow, 2) = "文件"
        Cells(currentRow, 3) = file.Size
        currentRow = currentRow + 1  ' 修改会反映到调用者
    Next file
    
    For Each subfolder In folder.SubFolders
        Cells(currentRow, 1) = indent & subfolder.Path
        Cells(currentRow, 2) = "文件夹"
        currentRow = currentRow + 1
        ' 递归调用时继续传递currentRow引用
        ListFiles_方案2 subfolder.Path, indent & "    ", currentRow
    Next subfolder
End Sub

' ========================================
' 解决方案3：使用模块级变量（推荐用于模块内共享）
' ========================================

Dim m_currentRow As Long  ' 模块级变量

Sub 列出目录结构_方案3()
    Dim folderPath As String
    
    With Application.FileDialog(msoFileDialogFolderPicker)
        .Title = "请选择要列出结构的文件夹"
        If .Show = -1 Then
            folderPath = .SelectedItems(1)
        Else
            Exit Sub
        End If
    End With
    
    ActiveSheet.Cells.Clear
    
    Cells(1, 1) = "文件路径"
    Cells(1, 2) = "类型"
    Cells(1, 3) = "大小(字节)"
    
    m_currentRow = 2  ' 初始化模块级变量
    
    ListFiles_方案3 folderPath, ""
End Sub

Sub ListFiles_方案3(ByVal folderPath As String, ByVal indent As String)
    Dim fso As Object
    Dim folder As Object
    Dim subfolder As Object
    Dim file As Object
    
    Set fso = CreateObject("Scripting.FileSystemObject")
    Set folder = fso.GetFolder(folderPath)
    
    For Each file In folder.Files
        Cells(m_currentRow, 1) = indent & file.Path  ' 使用模块级变量
        Cells(m_currentRow, 2) = "文件"
        Cells(m_currentRow, 3) = file.Size
        m_currentRow = m_currentRow + 1
    Next file
    
    For Each subfolder In folder.SubFolders
        Cells(m_currentRow, 1) = indent & subfolder.Path
        Cells(m_currentRow, 2) = "文件夹"
        m_currentRow = m_currentRow + 1
        ListFiles_方案3 subfolder.Path, indent & "    "
    Next subfolder
End Sub

' ========================================
' 【三种方案的比较】
' ========================================
'
' 方案1 - 全局变量 (Public)：
' 优点：简单易用，所有模块都可访问
' 缺点：可能被其他代码意外修改，不利于代码维护
' 适用：简单的单一功能程序
'
' 方案2 - 参数传递 (ByRef)：
' 优点：最安全，变量作用域明确，便于调试和维护
' 缺点：需要修改函数签名，递归调用时需要传递更多参数
' 适用：复杂程序，需要良好的代码结构
'
' 方案3 - 模块级变量 (Dim)：
' 优点：在模块内共享，比全局变量安全
' 缺点：仍然存在被意外修改的风险
' 适用：模块内多个函数需要共享状态的场景
'
' 【推荐】：对于这个具体案例，推荐使用方案2（参数传递），
' 因为它最符合良好的编程实践，便于调试和维护。
' ========================================
