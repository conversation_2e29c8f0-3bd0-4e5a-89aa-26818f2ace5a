Sub 列出目录结构()
    Dim folderPath As String
    
    '让用户选择要列出结构的文件夹
    With Application.FileDialog(msoFileDialogFolderPicker)
        .Title = "请选择要列出结构的文件夹"
        If .Show = -1 Then
            folderPath = .SelectedItems(1)
        Else
            Exit Sub
        End If
    End With
    
    '清空当前工作表
    ActiveSheet.Cells.Clear
    
    '写入表头
    Cells(1, 1) = "文件路径"
    Cells(1, 2) = "类型"
    Cells(1, 3) = "大小(字节)"
    
    '从第二行开始写入数据
    currentRow = 2
    
    '调用递归函数列出目录结构
    ListFiles folderPath, ""
End Sub

Sub ListFiles(ByVal folderPath As String, ByVal indent As String)
    Dim fso As Object
    Dim folder As Object
    Dim subfolder As Object
    Dim file As Object
    
    '创建文件系统对象
    Set fso = CreateObject("Scripting.FileSystemObject")
    Set folder = fso.GetFolder(folderPath)
    
    '先列出所有文件
    For Each file In folder.Files
        '写入文件信息
        Cells(currentRow, 1) = indent & file.Path
        Cells(currentRow, 2) = "文件"
        Cells(currentRow, 3) = file.Size
        currentRow = currentRow + 1
    Next file
    
    '再列出所有子文件夹
    For Each subfolder In folder.SubFolders
        '写入文件夹信息
        Cells(currentRow, 1) = indent & subfolder.Path
        Cells(currentRow, 2) = "文件夹"
        currentRow = currentRow + 1
        
        '递归调用，处理子文件夹
        ListFiles subfolder.Path, indent & "    "
    Next subfolder
End Sub
